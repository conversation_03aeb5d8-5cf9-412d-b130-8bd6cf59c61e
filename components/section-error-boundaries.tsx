"use client"

import React from "react"
import { ErrorBoundary } from "./error-boundary"
import { But<PERSON> } from "@/components/ui/button"

/**
 * Error boundary specifically for the hero section
 */
export function HeroErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
          <div className="text-center max-w-md mx-auto p-8">
            <div className="text-6xl mb-4">🔧</div>
            <h1 className="text-3xl font-bold text-slate-900 mb-4">
              Hero Section Unavailable
            </h1>
            <p className="text-slate-600 mb-6">
              The main hero section encountered an error. Please refresh the page to try again.
            </p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              Refresh Page
            </Button>
          </div>
        </div>
      }
      onError={(error) => {
        console.error("Hero section error:", error)
        // In production, send to error tracking service
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the spotlight animation system
 */
export function SpotlightErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="absolute inset-0 bg-slate-900 flex items-center justify-center">
          <div className="text-center text-white p-8">
            <div className="text-4xl mb-4">✨</div>
            <h3 className="text-xl font-semibold mb-2">Animation Unavailable</h3>
            <p className="text-slate-300 text-sm">
              The interactive spotlight effect couldn&apos;t load, but the content is still accessible.
            </p>
          </div>
        </div>
      }
      onError={(error) => {
        console.error("Spotlight animation error:", error)
        // Non-critical error - log but don&apos;t disrupt user experience
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the use cases carousel
 */
export function UseCasesErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="py-24 bg-white">
          <div className="container mx-auto px-8 text-center">
            <div className="text-5xl mb-6">📊</div>
            <h2 className="text-3xl font-bold text-slate-900 mb-4">
              Use Cases Section Unavailable
            </h2>
            <p className="text-slate-600 max-w-2xl mx-auto mb-8">
              We&apos;re having trouble loading the use cases carousel. Here are some key benefits of our AI troubleshooting agent:
            </p>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="p-6 bg-slate-50 rounded-lg">
                <h3 className="font-semibold text-slate-900 mb-2">Network Diagnostics</h3>
                <p className="text-slate-600 text-sm">Quickly identify and resolve network connectivity issues</p>
              </div>
              <div className="p-6 bg-slate-50 rounded-lg">
                <h3 className="font-semibold text-slate-900 mb-2">Performance Analysis</h3>
                <p className="text-slate-600 text-sm">Analyze system performance and optimize bottlenecks</p>
              </div>
              <div className="p-6 bg-slate-50 rounded-lg">
                <h3 className="font-semibold text-slate-900 mb-2">Error Resolution</h3>
                <p className="text-slate-600 text-sm">Automated error detection and solution recommendations</p>
              </div>
            </div>
          </div>
        </div>
      }
      onError={(error) => {
        console.error("Use cases section error:", error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the navigation component
 */
export function NavigationErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <nav className="flex items-center justify-between px-8 h-16 bg-white/95 backdrop-blur-sm sticky top-0 z-50 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-[42px] h-[42px] bg-emerald-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">42</span>
            </div>
          </div>
          <div className="text-slate-600 text-sm">
            Navigation temporarily unavailable
          </div>
        </nav>
      }
      onError={(error) => {
        console.error("Navigation error:", error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the typing animation
 */
export function TypingAnimationErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="bg-slate-900 text-emerald-400 p-6 rounded-lg font-mono text-sm">
          <div className="mb-2">$ ./troubleshoot --analyze</div>
          <div className="mb-2">Analyzing system...</div>
          <div className="mb-2">✓ Network diagnostics complete</div>
          <div className="mb-2">✓ Performance analysis complete</div>
          <div className="mb-2">✓ Error detection complete</div>
          <div className="mb-2">Ready to implement solutions</div>
          <div className="text-emerald-300">Animation temporarily unavailable</div>
        </div>
      }
      onError={(error) => {
        console.error("Typing animation error:", error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the features section
 */
export function FeaturesErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <section className="py-24 bg-slate-50">
          <div className="container mx-auto px-8 text-center">
            <div className="text-5xl mb-6">🛠️</div>
            <h2 className="text-3xl font-bold text-slate-900 mb-4">
              Features Section Unavailable
            </h2>
            <p className="text-slate-600 max-w-2xl mx-auto">
              We&apos;re having trouble loading the detailed features. Our AI troubleshooting agent provides
              comprehensive network diagnostics, automated problem detection, and intelligent solution recommendations.
            </p>
          </div>
        </section>
      }
      onError={(error) => {
        console.error("Features section error:", error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

/**
 * Error boundary for the CTA (Call to Action) section
 */
export function CTAErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <section className="py-24 bg-gradient-to-r from-emerald-600 to-emerald-700">
          <div className="container mx-auto px-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-emerald-100 mb-8">
              Start diagnosing and resolving issues with our AI-powered troubleshooting agent.
            </p>
            <Button
              size="lg"
              className="bg-white text-emerald-600 hover:bg-emerald-50 px-12 py-6 text-xl font-semibold"
            >
              Start Diagnosing Now
            </Button>
          </div>
        </section>
      }
      onError={(error) => {
        console.error("CTA section error:", error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}
