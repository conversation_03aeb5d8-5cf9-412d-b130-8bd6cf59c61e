# Testing Checklist for Dynamic Subtitle Feature

## Manual Testing Steps

### 1. Initial Load Test
- [ ] Navigate to http://localhost:3000
- [ ] Scroll down to the "Use Cases" section
- [ ] Verify the subtitle displays: "The hands stretch across the void, but the handshake never completes."
- [ ] Verify the first image (<PERSON><PERSON>'s Creation of Adam) is displayed

### 2. Desktop Navigation Test
- [ ] Click the right arrow (›) button on the carousel
- [ ] Verify the subtitle changes to: "Endless waiting bends time, memory becomes eternal, and users collapse."
- [ ] Verify the transition is smooth (500ms duration)
- [ ] Continue clicking through all 6 slides and verify each subtitle matches:
  - Slide 1: "The hands stretch across the void, but the handshake never completes."
  - Slide 2: "Endless waiting bends time, memory becomes eternal, and users collapse."
  - Slide 3: "The statue stands proud, yet the missing arms reveal the loss within."
  - Slide 4: "A broken voice echoes in distortion, crying for clarity that never comes."
  - Slide 5: "Packets wander the endless stairways, trapped in loops without escape."
  - Slide 6: "The giant wave of traffic crashes down, drowning the fragile link."
- [ ] Test the left arrow (‹) button for backward navigation
- [ ] Verify loop functionality (slide 6 → slide 1, slide 1 → slide 6)

### 3. Mobile Navigation Test
- [ ] Resize browser to mobile width (< 768px) or use mobile device
- [ ] Verify desktop overlay buttons are hidden
- [ ] Test "Previous" and "Next" buttons below the carousel
- [ ] Verify subtitle synchronization works on mobile

### 4. Rapid Navigation Test
- [ ] Click navigation buttons rapidly
- [ ] Verify subtitle always matches the current image
- [ ] Verify no visual glitches or text overlap during transitions

### 5. Accessibility Test
- [ ] Use keyboard navigation (Tab to buttons, Enter to activate)
- [ ] Verify subtitle changes are announced by screen readers
- [ ] Check that aria-labels are present on navigation buttons

### 6. Visual Consistency Test
- [ ] Verify subtitle maintains original font family (Space Grotesk)
- [ ] Verify subtitle maintains original font size (text-xl)
- [ ] Verify subtitle maintains original color (text-slate-600)
- [ ] Verify subtitle maintains original font weight (font-light)

## Expected Results

### Subtitle Text Verification
Each slide should display the exact subtitle text as specified:

| Slide | Expected Subtitle |
|-------|------------------|
| 1 | The hands stretch across the void, but the handshake never completes. |
| 2 | Endless waiting bends time, memory becomes eternal, and users collapse. |
| 3 | The statue stands proud, yet the missing arms reveal the loss within. |
| 4 | A broken voice echoes in distortion, crying for clarity that never comes. |
| 5 | Packets wander the endless stairways, trapped in loops without escape. |
| 6 | The giant wave of traffic crashes down, drowning the fragile link. |

### Performance Expectations
- [ ] Smooth transitions without lag
- [ ] No console errors in browser developer tools
- [ ] No memory leaks during extended navigation
- [ ] Responsive behavior across different screen sizes

## Troubleshooting

### Common Issues
1. **Subtitle not changing**: Check browser console for JavaScript errors
2. **Transition too fast/slow**: Verify CSS transition duration is 500ms
3. **Wrong subtitle text**: Verify array index mapping in useCases data
4. **Mobile buttons not working**: Check responsive CSS classes

### Debug Steps
1. Open browser developer tools
2. Check Console tab for errors
3. Inspect Elements tab to verify DOM structure
4. Use React Developer Tools to check component state
5. Verify currentIndex state updates correctly
