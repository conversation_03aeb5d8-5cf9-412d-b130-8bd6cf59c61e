# Dynamic Subtitle Implementation for Use Cases Section

## Overview
Modified the Use Cases section to have a dynamic subtitle that changes in sync with the image carousel. The subtitle now displays poetic metaphors corresponding to each artwork image.

## Changes Made

### 1. Data Structure Enhancement
- Added `subtitle` property to each use case object in the `useCases` array
- Each subtitle corresponds to the METAPHOR (EN) column from the requirements table

### 2. State Management
- Added `currentIndex` state to track the active slide
- Implemented `onSelect` callback to update the current index when slides change
- Added useEffect to listen for Embla carousel events (`select` and `reInit`)

### 3. Dynamic Subtitle Display
- Modified the subtitle paragraph to display `useCases[currentIndex]?.subtitle`
- Added smooth transition with `transition-all duration-500 ease-in-out` classes
- Included fallback text for safety

### 4. Subtitle Content Mapping
| Image ID | Artwork Reference | Subtitle Text |
|----------|------------------|---------------|
| use_case_1 | <PERSON><PERSON> – *The Creation of Adam* | The hands stretch across the void, but the handshake never completes. |
| use_case_2 | Salvador Dalí – *The Persistence of Memory* | Endless waiting bends time, memory becomes eternal, and users collapse. |
| use_case_3 | Venus de Milo | The statue stands proud, yet the missing arms reveal the loss within. |
| use_case_4 | Edvard Munch – *The Scream* | A broken voice echoes in distortion, crying for clarity that never comes. |
| use_case_5 | M.C. Escher – *Relativity* | Packets wander the endless stairways, trapped in loops without escape. |
| use_case_6 | Katsushika Hokusai – *The Great Wave off Kanagawa* | The giant wave of traffic crashes down, drowning the fragile link. |

## Technical Implementation Details

### Synchronization
- Uses Embla Carousel's built-in event system for perfect synchronization
- `emblaApi.selectedScrollSnap()` provides the current slide index
- Events handled: `select` (when slide changes) and `reInit` (when carousel reinitializes)

### Smooth Transitions
- CSS transition applied to subtitle paragraph: `transition-all duration-500 ease-in-out`
- 500ms duration provides smooth but not sluggish transitions
- Maintains existing font family (`font-space-grotesk`) and size (`text-xl`)

### Navigation Support
- Works with all navigation methods:
  - Manual navigation (desktop overlay buttons)
  - Mobile navigation buttons
  - Keyboard navigation (if implemented)
  - Auto-advance (if implemented in future)
  - Touch/swipe gestures

### Error Handling
- Optional chaining (`useCases[currentIndex]?.subtitle`) prevents errors
- Fallback text ensures subtitle is never empty
- Proper cleanup of event listeners in useEffect

## Files Modified
- `components/use-cases.tsx` - Main implementation
- `docs/dev/dynamic-subtitle-implementation.md` - This documentation

## Testing Recommendations
1. Test manual navigation with desktop overlay buttons
2. Test mobile navigation buttons
3. Verify smooth transitions between all slides
4. Check that subtitle text matches exactly as specified
5. Test edge cases (rapid navigation, carousel reinitialization)
6. Verify accessibility (screen readers should announce subtitle changes)
