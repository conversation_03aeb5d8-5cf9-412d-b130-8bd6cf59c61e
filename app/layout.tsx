import type React from "react"
import type { Metadata } from "next"
import { Space_Grotesk, DM_Sans, Roboto_Mono } from "next/font/google"
import "./globals.css"
import { RootErrorBoundary } from "@/components/root-error-boundary"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-space-grotesk",
})

const dmSans = DM_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dm-sans",
})

const robotoMono = Roboto_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-roboto-mono",
})

export const metadata: Metadata = {
  title: "42 - AI Troubleshooting Agent",
  description: "Empower your troubleshooting with intelligent AI diagnostics",
    generator: 'v0.app'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${spaceGrotesk.variable} ${dmSans.variable} ${robotoMono.variable} antialiased`}>
      <body className="font-sans">
        <RootErrorBoundary>
          {children}
        </RootErrorBoundary>
      </body>
    </html>
  )
}
